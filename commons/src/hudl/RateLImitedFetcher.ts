import {timestamp} from "@playerlynk/shared";
import moment from "moment-timezone";
import axios from "axios";

const DEFAULT_RATE_LIMITATION = 1000;

export type RateLimitedRequest<T> = {
	url: string;
	query: any;
	headers: any;
	resolve: (value: any) => void;
	reject: (reason?: any) => void;
}

export class RateLimitedFetcher {
	private lastRequestTime: timestamp;
	private rateLimitation: number;
	private processingTimer: any = null;
	private processing = false;
	private debug = true;

	private queues: RateLimitedRequest<any>[] = [];

	constructor(rateLimitation: number = DEFAULT_RATE_LIMITATION, debug: boolean = true) {
		this.lastRequestTime = 0;
		this.rateLimitation = rateLimitation;
		this.debug = debug;
	}

	async fetch(url: string, query?: any, headers?: any): Promise<any> {
		return new Promise((resolve, reject) => {
			this.queues.push({
				url,
				query,
				headers,
				resolve,
				reject
			});
			this.wakeUp();
		});
	}

	wakeUp() {
		this.stopTimer();
		if(!this.processing) {
			this.processQueue();
		}
	}

	stopTimer() {
		if(this.processingTimer) {
			clearTimeout(this.processingTimer);
			this.processingTimer = null;
		}
	}

	runTimer(forced: boolean = false): boolean {
		const timeSinceLastRequest = moment.now() - this.lastRequestTime;
		const waitTime = Math.max(0, this.rateLimitation - timeSinceLastRequest);
		if (forced || waitTime > 0) {
			this.processingTimer = setTimeout(() => {
				this.processQueue();
			}, waitTime);
			return true;
		}
		else{
			return false;
		}
	}

	async processQueue() {
		// console.log("Process Queue: ", this.queues.length);
		if(this.processing) {
			return;
		}
		if (this.queues.length === 0) {
			return;
		}

		this.processing = true;
		this.stopTimer();

		if(this.runTimer()) {
			this.processing = false;
			return;
		}

		const request = this.queues.shift();
		if(request) {
			const url = this.buildUrl(request.url, request.query);
			if(this.debug) {
				console.log("URL: " + url);
			}
			this.lastRequestTime = moment.now();
			try {
				const response = await axios.get(url, {headers: request.headers});
				request.resolve(response.data);
			}
			catch (err: any) {
				request.reject(err.response.data);
			}
		}

		this.processing = false;
		this.runTimer(true);
	}

	private buildUrl(url: string, query?: any): string {
		let fullUrl = url;
		if(query) {
			let separator = "?";
			for(const key of Object.keys(query)) {
				const value = (query[key] !== null && query[key] !== undefined) ? encodeURIComponent(query[key].toString()) : null;
				if(value !== null) {
					fullUrl += separator + key + "=" + encodeURIComponent(query[key].toString());
					separator = "&";
				}
			}
		}
		return fullUrl;
	}

}
