import {IDatabase} from "@snark/mongodb-operator";

export type HudlTournament = {
	id: number
	name: string
	country_id: number
	country_name: string
	ts: string
}

export type HudlSeason = {
	id: string
	name: string
	date_from: string
	date_to: string
}

export type HudlMatch = {
	id: string
	match_date: string
	tournament_id: string
	tournament_name: string
	season_id: string
	season_name: string
	team1_id: string
	team1_name: string
	team2_id: string
	team2_name: string
	team1_score: string
	team2_score: string
	status_id: string
	status_name: string
	match_name: string
	ts: string
}

export type HudlMatchLineups = {
	first_team: HudlMatchLineupTeam[]
	second_team: HudlMatchLineupTeam[]
}

export type HudlMatchLineupTeam = {
	id: string
	name: string
	color_1: string
	color_2: string
	color_num: string
	lineup: {
		main: {
			player: HudlSimplePlayer[]
		}[]
	}[]
}

export type HudlMinimalPlayer = {
	id: string
	firstname: string
	lastname: string

}

export type HudlSimplePlayer = HudlMinimalPlayer & {
	position_id: string
	position_name: string
	birthday: string
	country1_id: string
	country1_name: string
}

export type HudlPlayerInfo = HudlSimplePlayer & {
	id: number,
	club_team_id: number,
	club_team_name: string,
	club_number: number,
	national_number: number,
	position1_id: number,
	position1_name: string,
	hand_id: number,
	hand_name: "Left"|"Right",
	gender_id: number,
	gender_name: "F"|"M",
	height: number,
	weight: number,
	photo: string
	ts: string
}

export type HudlSimpleTeam = {
	id: number
	name: string
	short_name: string
}

export type HudlTeam = HudlSimpleTeam & {
	team_type_id: number
	team_type_name: string
	country_id: number
	country_name: string
	gender_id: number
	gender_name: string
	photo: string
	ts: string
}

export type HudlPlayerStats = {
	id: string
	name: string
	number: string
	param: HudlStatParam[]
}

export type HudlTeamStats = {
	id: string
	name: string
	param: HudlStatParam[]
}

export type HudlStatParam = {
	id: string
	name: string
	value: string

	// A RETIRER Si on ne s'en sert pas.
	t1?: string
	t2?: string
	t3?: string
	t4?: string

	// --- ce sont les stats par zones de jeu (voir le PDF sur l'API dans /data/hudl)
	z1?: string
	z2?: string
	z3?: string
	z4?: string
	z5?: string
	z6?: string
	z7?: string
	z8?: string
	z9?: string
	z10?: string
	z11?: string
	z12?: string
	z13?: string
	z14?: string
	z15?: string
	z16?: string
	z17?: string
	z18?: string
	z19?: string
	z20?: string
	z21?: string
	z22?: string
	z23?: string
	z24?: string
	z25?: string
	z26?: string
	z27?: string
	z28?: string
	z29?: string
	z30?: string
}

export type HudlSquadPlayer = {
	id: string
	firstname: string
	lastname: string
	club_team_id: string
	club_team_name: string
	club_number?: string
	national_team_id?: string
	national_team_name?: string
	national_number?: string
	ts: string
}

export type HudlPlayerSeasonStats = {
	id: number
	tournamentId: number
	tournamentName: string
	seasonId: number
	season: string
	firstname: string
	lastname: string
	param: HudlAggregatedStatParam[]
}

export type HudlAggregatedStatParam = {
	id: string
	name: string
	value_sum: number
	value_avg: number
}

export type HudlApiConfiguration = {
	id: string
	key: string
	apiUrl: string
	rateLimitation: number
	db: IDatabase
}