{"name": "@playerlynk/commons", "version": "1.0.0", "license": "ISC", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "build-watch": "tsc -w"}, "_moduleAliases": {"@shared": "dist/shared/"}, "devDependencies": {"@types/node": "^22.17.2", "@types/source-map-support": "^0", "source-map-support": "^0.5.21", "tsconfig-paths": "^4.2.0", "typescript": "^5.9.2"}, "dependencies": {"@playerlynk/shared": "*", "@snark/mongodb-operator": "^3.3.2", "axios": "^1.11.0", "moment-timezone": "^0.6.0"}}