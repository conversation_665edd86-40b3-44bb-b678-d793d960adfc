import type { objectId, timestamp } from "./baseTypes";
import { NotificationType, SearchMissingData, SortOrder } from "./enums";
import { IMatchDB, type IPlayerDB, SearchContext } from "./crudTypes";

export * from "./baseTypes";
export * from "./crudTypes";

// ----------------------------------------------------------------------------
// Notifications

export type NotificationSender = {
  userId?: objectId;
};

export interface IRawNotification {
  sender: NotificationSender;
  type: NotificationType;
  data: any;
  timestamp: timestamp;
  noSenderNotification?: boolean;
}

export interface IPerson {
  firstname: string;
  lastname: string;
}

export interface IAddress {
  name: string;
  street: string;
  complement: string;
  city: string;
  zip: string;
  countryCode: string;
  location: ILocation;
}

export interface ILocation {
  lat: string;
  lng: string;
}

export type SortField = {
  field: string;
  order?: SortOrder;
};

export type PaginationOption = {
  offset?: number;
  limit?: number;
};

// ----------------------------------------------------------
// Integration stats et recherche GPT
export type Season = {
  id: string;
  name: string;
  date_from: string;
  date_to: string;
};

export type GlobalStat = {
  id: string;
  name: string;
  min: number;
  max: number;
};

export type Metrics = {
  name: string;
  weight: number;
};

export type MatchStatData = {
  id: string;
  name: string;
  value: string;
};

export type MatchStat = {
  _id: objectId;
  external_match_id: string;
  external_player_id: string;
  external_statistics: MatchStatData[];
  external_ts: Date;
};

export type MatchWithStat = IMatchDB & {
  playerStats?: MatchStat; // peut être populé pendant les process
};

export type StatDetail = {
  id: string;
  name: string;
  total: number;
  count: number;
  avg: number | null;
  max: number | null;
  min: number | null;
};

export type BestPlayerResultSuccess = {
  success: boolean;
  message: string;
  players: IPlayerDB[];
};

export type BestPlayerResultNeeds = {
  success: boolean;
  needs: string;
};

export type BestPlayerResult = BestPlayerResultSuccess | BestPlayerResultNeeds;

export type VerifierResponse = {
  needMore: boolean;
  context: SearchContext;
  data?: {
    missingData?: SearchMissingData;
    question?: string;
  };
};

export type VerifierFunction = (
  prompt: string,
  context: SearchContext
) => Promise<VerifierResponse>;

// --------------------------------------
// TEMP
export type ComputedMetric = {
  label: string;
  value: number;
  isPercentage?: boolean;
};

// --------------------------------------
// STATS

export type Badge = {
  label: string;
  description: string;
  type: string; // for now only "standard" displayed in blue
};

export enum InsightType {
  STRENGTHS = "strengths",
  DEVELOPMENT = "development",
}

export type Insight = {
  type: InsightType; // for now "strengths" or "development"
  value: Array<string>;
};

export type RecentForm = {
  label: string; // for now "PG", "QF", "SF", "Final"
  wins: number;
  losses: number;
};

export type UpsidePotentialScore = {
  value: number;
  avg: number;
};

export type PlayerOverview = {
  seasonId: string;
  seasonName: string;
  data: Array<{
    labelSlug: string; // slug displayed with i18n
    value: number; // percent
  }>;
};

export type Scoring = {
  trueShooting: number; // percent
  pointsPerPossession: number;
  pointsPerPossessionMax: number;
};

export type Efficiency = {
  labelSlug: string; // slug displayed with i18n
  value: number;
  avg: number;
  maxValue: number;
  percent: boolean;
};

export type Defense = {
  metrics: Array<{
    labelSlug: string; // slug displayed with i18n
    value: number;
    avg: number;
    maxValue: number;
    percent: boolean;
  }>;
  opponentFG: number; // percent
};

export type PlayMaking = {
  metrics: Array<{
    labelSlug: string; // // slug displayed with i18n
    value: number;
    maxValue: number;
    percent: boolean;
  }>;
};

export type PlayerCompleteStat = {
  playerId: string;
  player: IPlayerDB; // populated player data
  badges: Array<Badge>;
  insights: Array<Insight>;
  recentForm: Array<RecentForm>;
  upsidePotentialScore: UpsidePotentialScore;
  overview: Array<PlayerOverview>;
  scoring: Scoring;
  efficiency: Array<Efficiency>;
  defense: Defense;
  playMaking: PlayMaking;
  similarPlayerIds: Array<string>; // array of player Ids
  similarPlayers: Array<IPlayerDB>; // array of populated similar players
};
