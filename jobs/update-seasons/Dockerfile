# Use the official lightweight Node.js runtime image
FROM node:22-alpine

# Create app directory
WORKDIR /app

COPY package.json yarn.lock lerna.json ./
COPY commons/package.json commons/yarn.lock ./commons/
COPY jobs/*/package.json jobs/*/yarn.lock ./jobs/*/

# Install only production deps for my-service and its monorepo deps
RUN npx lerna bootstrap \
    --scope @playerlynk/job-update-seasons \
    --include-dependencies \
    -- --production

COPY commons/dist ./commons/
COPY jobs/*/dist ./jobs/

# Set environment
ENV NODE_ENV=production

# Run the job
WORKDIR /app/jobs/update-seasons
CMD ["node", "dist/index.js"]