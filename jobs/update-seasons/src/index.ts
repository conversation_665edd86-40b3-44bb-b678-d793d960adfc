import {HudlApi, HudlApiConfiguration} from "@playerlynk/commons";
import moment from "moment-timezone";
import {DBCollection} from "@shared/enums";
import {HudlSeason} from "@playerlynk/commons";
import {ServerApiVersion} from "mongodb";
import Database from "@snark/mongodb-operator";

/**
 * Get all seasons from Hudl and update our local database.
 */
export default async function updateHudlSeasons(): Promise<void> {
	try {
		console.log("🤖 UPDATE HUDL SEASONS JOB");

		console.log("Database connection");
		const databaseConfiguration = {
			mongo: {
				autoConnect: false,
				atlas: true,
				gcloud: {
					projectId: "snappy-cosine-439512-i3",
					secretName: "databaseUrlVPC",
					secretVersion: "latest",
				},
				database: "playerlynk",
				mongoOptions: {
					directConnection: false,
					serverSelectionTimeoutMS: 20000,
					serverApi: {
						version: ServerApiVersion.v1,
						strict: true,
						deprecationErrors: true,
					}
				}
			},
			logger: null
		};
		const db = new Database(databaseConfiguration);
		await db.connect();
		console.log("Database connected");

		console.log("🤖 STARTING UPDATE HUDL SEASONS");

		const hudlConfig: HudlApiConfiguration = {
			id: "1193838",
			key: "NbAJZCEl",
			rateLimitation: 1000,
			apiUrl: "https://service.instatfootball.com/feed.php",
			db
		}
;
		const api = new HudlApi(hudlConfig);

		// --- get all seasons
		const seasons: HudlSeason[] = await api.getAllSeasons();
		console.log(`Found ${seasons.length} seasons`);
		for(const s of seasons) {
			await db.updateOne(DBCollection.hudlSeason, {id: s.id}, {$set: {
				hudlId: s.id,
				name: s.name,
				start: moment(s.date_from, "YYYY-MM-DD HH:mm:ss").valueOf(),
				end: moment(s.date_to, "YYYY-MM-DD HH:mm:ss").valueOf()
			}}, {upsert: true});
		}

		console.log("✅  END OF UPDATE HUDL SEASONS");
	}
	catch(err) {
		console.error("❌ ERROR ON HUDL UPDATE SEASONS");
		console.error(err);
	}
}

// -----------------------------------------------------------------------------
// Main

(async function() {
	try {
		await updateHudlSeasons();
		process.exit(0);
	}
	catch(err) {
		console.error("GLOBAL ERROR: ");
		console.error(err);
		process.exit(-1);
	}
})();
